import io
from datetime import datetime

import pandas as pd
from django.db.models.query import RawQuerySet
from django.http import HttpResponse
from django.utils.encoding import escape_uri_path

from income import const


def export_to_excel(queryset: RawQuerySet):
    data_list = [row.__dict__ for row in queryset]
    drop_labels = [
        "_state",
        "id",
        "income_adjust_id",
        "updated_at",
        "bill_used_nums",
        "group_approve_state",
    ]
    # 创建DataFrame
    if not data_list:
        # 如果没有数据,创建空的DataFrame
        charge_df = pd.DataFrame(
            columns=list(const.FIELD_MAPPING.values()) + drop_labels,
        )
    else:
        charge_df = pd.DataFrame(data_list)
    charge_df = charge_df.drop(
        labels=[
            "_state",
            "id",
            "income_adjust_id",
            "updated_at",
            "bill_used_nums",
            "group_approve_state",
        ],
        axis=1,
    )
    charge_df = charge_df.rename(columns=const.FIELD_MAPPING)
    # 生成Excel文件
    bio = io.BytesIO()
    with pd.ExcelWriter(bio, engine="openpyxl") as writer:
        charge_df.to_excel(writer, sheet_name="出账明细", index=False)
    bio.seek(0)

    # 生成文件名
    filename = f"出账明细_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    # 创建HTTP响应
    response = HttpResponse(
        content=bio.getvalue(),
        content_type="application/vnd.ms-excel",
    )
    response["Content-Disposition"] = (
        f'attachment; filename="{escape_uri_path(filename)}"'
    )
    return response
