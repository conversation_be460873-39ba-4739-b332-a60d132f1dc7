from rest_framework import serializers

from .models import IncomeChargeDetail


class IncomeChargeDetailRawSerializer(serializers.ModelSerializer):
    """
    专门处理Raw SQL查询结果的序列化器
    Raw查询已经包含了所有关联字段,无需额外查询
    """

    customer_name = serializers.CharField(read_only=True, help_text="customer_name")
    sale_name = serializers.CharField(read_only=True, help_text="sale_name")
    sign_contract_entity = serializers.CharField(
        read_only=True,
        help_text="sign_contract_entity",
    )
    contract_legal_num = serializers.CharField(
        read_only=True,
        help_text="contract_legal_num",
    )
    adjust_month = serializers.CharField(read_only=True, help_text="adjust_month")
    adjust_reason_class = serializers.CharField(
        read_only=True,
        help_text="adjust_reason_class",
    )
    adjust_reason = serializers.CharField(read_only=True, help_text="adjust_reason")

    class Meta:
        model = IncomeChargeDetail
        exclude = ("updated_at",)
