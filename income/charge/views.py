from drf_spectacular.utils import extend_schema
from drf_spectacular.utils import extend_schema_view
from rest_framework import mixins
from rest_framework.decorators import action

from income.contrib.drf.views import GenericViewSet
from income.permissions import IsAuthenticated
from income.permissions import RoleMenuPermission

from .filters import IncomeChargeDetailFieldsFilter
from .models import IncomeChargeDetail
from .serializers import IncomeChargeDetailRawSerializer
from .utils import export_to_excel


@extend_schema_view(
    list=extend_schema(summary="获取出账明细信息"),
)
@extend_schema(tags=["charge-detail"])
class IncomeChargeDetailViewSet(
    GenericViewSet,
    mixins.ListModelMixin,
):
    serializer_class = IncomeChargeDetailRawSerializer
    filter_backends = [IncomeChargeDetailFieldsFilter]
    search_fields = ["order_no", "charge_month", "adjust_month"]
    search_contains = True
    table_mapping = {
        "order_no": "a",
        "charge_month": "a",
        "adjust_month": "d",
    }

    permission_classes = [IsAuthenticated, RoleMenuPermission]

    def get_queryset(self):
        """
        使用Raw SQL获取完整数据, 减少内存使用
        """
        return self._get_raw_queryset()

    def _get_raw_queryset(self):
        """使用Raw SQL获取完整数据"""
        sql = """
        SELECT a.*,
               b.`customer_name`,
               c.`sale_name`,
               c.`sign_contract_entity`,
               c.`contract_legal_num`,
               d.`adjust_month`,
               d.`adjust_reason_class`,
               d.`adjust_reason`
        FROM `income_charge_detail` a
        LEFT JOIN `customer_info` b ON a.`customer_num` = b.`customer_num`
        LEFT JOIN `contract_info` c ON a.`contract_num` = c.`contract_num`
        LEFT JOIN `income_adjust_detail` d ON a.`income_adjust_id` = d.id
        ORDER BY a.`created_at` DESC
        """
        return IncomeChargeDetail.objects.raw(sql)

    @extend_schema(
        summary="导出出账明细数据为Excel文件",
        description="根据筛选条件导出出账明细数据为Excel文件",
        responses={200: {"description": "Excel文件下载"}},
    )
    @action(
        methods=["get"],
        detail=False,
        url_path="export",
        filter_backends=[IncomeChargeDetailFieldsFilter],
        pagination_class=None,
    )
    def export(self, request, *args, **kwargs):
        """导出出账明细数据为Excel文件"""
        # 获取筛选后的数据
        queryset = self.filter_queryset(self.get_queryset())

        # 转换为DataFrame并导出Excel
        return export_to_excel(queryset)
