# 提示信息配置文件
# -----------------------------------------------------------------------------
NOT_FOUND = "Not Found"

USERNAME_OR_PASSWORD_ERROR = "用户名或密码有误"  # noqa: S105
LOGIN_SUCCESS = "登录成功"
LOGOUT_SUCCESS = "登出成功"

# 客户管理
CUSTOMER_NOT_FOUND = "该客户信息不存在或您无权访问"
SALE_NAME_NOT_FOUND = "该销售不存在请重新进行选择"
CONTACT_NOT_FOUND = "该客户联系人信息不存在或您无权访问"
CUSTOMER_APPROVE_SUBMIT_ERROR = "只有初始状态的客户信息才能提交审核"
CUSTOMER_APPROVE_REVOKE_ERROR = "只有待审核状态的客户信息才能撤回"
CUSTOMER_APPROVE_APPROVE_ERROR = "只有待审核状态的客户信息才能审批或驳回"

# 角色
ROLE_NOT_FOUND = "请选择合规的角色"
ROLE_CODE_ERROR = "角色编码不能设置为admin"

# 菜单
MENU_NOT_FOUND = "请选择合规的菜单"

# 部门
DEPARTMENT_NOT_FOUND = "不存在该部门或您无权访问"

# 分账序号
ACCOUNT_SEQ_EXISTS = "该客户下存在相同的分账序号,请重新输入"

# 合同
CONTRACT_NOT_FOUND = "该合同信息不存在或您无权访问"

# 订单
ORDER_TOTAL_NUM_EXIStS = "合成编号重复,请重新生成"
ORDER_SERVICE_STATUS_NOT_MODIFY = "只有新装暂存订单才可支持编辑操作"
ORDER_SERVICE_STATUS_NOT_CHANGE_BILL_STATUS= "当前的服务状态的订单不支持计费审核"

# 发票信息
INVOICE_INFO_NOT_FOUND = "该发票信息不存在或您无权访问"
INVALID_DATE_RANGE = "开账开始日期不能晚于结束日期"

# 费用
FEE_PACKAGE_NAME_EXISTS = "套餐名称已存在"
FEE_INSTANCE_NOT_FOUND = "套餐实例不存在或您无权访问"
FEE_PACKAGE_NOT_FOUND = "套餐不存在或您无权访问"
FEE_TEMPLATE_NOT_FOUND = "套餐模板不存在或您无权访问"
FEE_INSTANCE_LEVEL_NOT_FOUND = "费用实例等级不存在或您无权访问"
FEE_TEMPLATE_NOT_LEVEL_FEE = "该实例的费用模板不支持阶梯计费,无法创建费用实例等级"
FEE_INSTANCE_HAS_LEVEL_DATA = "该费用实例下存在等级信息,不能修改为非阶梯计费模板"
